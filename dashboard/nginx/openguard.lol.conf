server {
    server_name  openguard.lol;

    # Marketing site served by Astro's Node server
    location / {
        proxy_pass http://127.0.0.1:4321/;
        proxy_set_header Host $host;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header Upgrade $http_upgrade;
    }

    location /dashboard {
        return 301 /dashboard/;
    }

    location /dashboard/ {
        root /srv/http/;
        index index.html;
        try_files $uri $uri/ /dashboard/index.html;
    }

    location /api/ {
	error_page 502 =500 /plaintext_404_api_login;
        proxy_pass http://127.0.0.1:5030/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location = /plaintext_404_api_login {
        internal; # This location can only be accessed by internal redirects
        default_type text/plain;
        return 200 "502 Bad Gateway. Is the backend API running?";
    }

    error_page 404 /404.html;

    location = /40x.html {
    }

    error_page 500 502 503 504 /50x.html;

    location = /50x.html {
    }

    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/openguard.lol/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/openguard.lol/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot

}
server {
    if ($host = openguard.lol) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


    listen       80;
    server_name  openguard.lol;
    return 404; # managed by Certbot


}