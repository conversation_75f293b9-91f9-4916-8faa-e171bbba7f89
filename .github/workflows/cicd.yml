name: CI/CD

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build_and_test:
    name: "Build & Test"
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
      - name: Enable Corepack
        run: corepack enable
      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "dir=$(yarn config get cacheFolder)" >> $GITHUB_OUTPUT
      - uses: actions/cache@v4
        id: yarn-cache
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-
      - name: Install Node.js dependencies
        run: yarn install --immutable
      - name: Lint Node.js projects
        run: yarn workspaces foreach --all run lint
      - name: Run Node.js tests
        run: yarn workspaces foreach --all run test
      - name: Build Node.js projects
        run: yarn workspaces foreach --all run build

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Install uv
        run: curl -LsSf https://astral.sh/uv/install.sh | sh

      - name: Add uv to PATH
        run: echo "$HOME/.cargo/bin" >> $GITHUB_PATH

      - name: Cache uv packages
        uses: actions/cache@v4
        with:
          path: ~/.cache/uv
          key: ${{ runner.os }}-uv-${{ hashFiles('**/requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-uv-

      - name: Install Python dependencies
        run: uv pip install -r pyproject.toml --all-extras -r dashboard/backend/requirements.txt pyright pytest ruff

      - name: Run Ruff
        run: ruff check bot.py cogs/ dashboard/backend --output-format=github

      - name: Run Pyright
        run: pyright

      - name: Run Python Tests
        run: pytest

  changes:
    name: "Detect Changes"
    needs: build_and_test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    permissions:
      contents: read
    outputs:
      website: ${{ steps.filter.outputs.website }}
      frontend: ${{ steps.filter.outputs.frontend }}
      backend: ${{ steps.filter.outputs.backend }}
      bot: ${{ steps.filter.outputs.bot }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 2
      - name: Filter changed paths
        uses: dorny/paths-filter@v2
        id: filter
        with:
          filters: |
            website:
              - 'website/**'
            frontend:
              - 'dashboard/frontend/**'
            backend:
              - 'dashboard/backend/**'
            bot:
              - 'bot.py'
              - 'cogs/**'
              - 'database/**'
              - 'requirements.txt'

  deploy-website:
    name: "Deploy Website"
    needs: changes
    if: needs.changes.outputs.website == 'true'
    runs-on: ubuntu-latest
    permissions:
      contents: read
      deployments: write
      statuses: write
    steps:
      - name: Create GitHub Deployment
        uses: bobheadxi/deployments@v1
        id: deployment
        with:
          step: start
          token: ${{ secrets.GITHUB_TOKEN }}
          env: production
          ref: ${{ github.sha }}
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
      - name: Enable Corepack
        run: corepack enable
      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "dir=$(yarn config get cacheFolder)" >> $GITHUB_OUTPUT
      - uses: actions/cache@v4
        id: yarn-cache
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-
      - name: Install dependencies
        run: yarn install --immutable
      - name: Build Website
        run: yarn build
      - name: Install Wrangler
        run: yarn global add wrangler
      - name: Deploy Website
        run: wrangler deploy
        working-directory: ./website
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
      - name: Update deployment status
        uses: bobheadxi/deployments@v1
        if: always()
        with:
          step: finish
          token: ${{ secrets.GITHUB_TOKEN }}
          status: ${{ job.status }}
          deployment_id: ${{ steps.deployment.outputs.deployment_id }}
          env: production

  deploy-dashboard-frontend:
    name: "Deploy Dashboard Frontend"
    needs: changes
    if: needs.changes.outputs.frontend == 'true'
    runs-on: ubuntu-latest
    permissions:
      contents: read
      deployments: write
      statuses: write
    steps:
      - name: Create GitHub Deployment
        uses: bobheadxi/deployments@v1
        id: deployment
        with:
          step: start
          token: ${{ secrets.GITHUB_TOKEN }}
          env: frontend-prod
          ref: ${{ github.sha }}
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
      - name: Enable Corepack
        run: corepack enable
      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "dir=$(yarn config get cacheFolder)" >> $GITHUB_OUTPUT
      - uses: actions/cache@v4
        id: yarn-cache
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-
      - name: Install dependencies
        run: yarn install --immutable
      - name: Build Dashboard Frontend
        run: yarn build
        env:
          VITE_DISCORD_CLIENT_ID: ${{ vars.VITE_DISCORD_CLIENT_ID }}
      - name: Install Wrangler
        run: yarn global add wrangler
      - name: Deploy Dashboard Frontend
        run: wrangler deploy
        working-directory: ./dashboard/frontend
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
      - name: Update deployment status
        uses: bobheadxi/deployments@v1
        if: always()
        with:
          step: finish
          token: ${{ secrets.GITHUB_TOKEN }}
          status: ${{ job.status }}
          deployment_id: ${{ steps.deployment.outputs.deployment_id }}
          env: frontend-prod

  deploy-dashboard-backend:
    name: "Deploy Dashboard Backend"
    needs: changes
    if: needs.changes.outputs.backend == 'true'
    runs-on: ubuntu-latest
    permissions:
      deployments: write
      statuses: write
    steps:
      - name: Create GitHub Deployment
        uses: bobheadxi/deployments@v1
        id: deployment
        with:
          step: start
          token: ${{ secrets.GITHUB_TOKEN }}
          env: backend-prod
          ref: ${{ github.sha }}
      - name: Deploy Dashboard Backend
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ vars.SSH_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            cd /home/<USER>/openguard
            git pull origin main
            sudo /home/<USER>/openguard/scripts/deploy_backend.sh
      - name: Update deployment status
        uses: bobheadxi/deployments@v1
        if: always()
        with:
          step: finish
          token: ${{ secrets.GITHUB_TOKEN }}
          status: ${{ job.status }}
          deployment_id: ${{ steps.deployment.outputs.deployment_id }}
          env: backend-prod

  deploy-bot:
    name: "Deploy Bot"
    needs: changes
    if: needs.changes.outputs.bot == 'true'
    runs-on: ubuntu-latest
    permissions:
      deployments: write
      statuses: write
    steps:
      - name: Create GitHub Deployment
        uses: bobheadxi/deployments@v1
        id: deployment
        with:
          step: start
          token: ${{ secrets.GITHUB_TOKEN }}
          env: bot-prod
          ref: ${{ github.sha }}
      - name: Deploy Discord Bot
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ vars.SSH_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            cd /home/<USER>/openguard
            git pull origin main
            uv pip install -r pyproject.toml --all-extras
            sudo systemctl restart openguard-bot.service
      - name: Update deployment status
        uses: bobheadxi/deployments@v1
        if: always()
        with:
          step: finish
          token: ${{ secrets.GITHUB_TOKEN }}
          status: ${{ job.status }}
          deployment_id: ${{ steps.deployment.outputs.deployment_id }}
          env: bot-prod

