[project]
name = "openguard"
version = "0.1.0"
description = "Open source Discord bot for moderation and management."
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "aiofiles>=24.1.0",
    "aiohttp>=3.12.14",
    "aiosqlite>=0.21.0",
    "asyncpg>=0.30.0",
    "cachetools>=6.1.0",
    "cryptography>=45.0.5",
    "discord-py-slipstream-fork>=2.6.0a0",
    "gputil>=1.4.0",
    "humanize>=4.12.3",
    "litellm-slipstream-fork>=1.74.7",
    "numpy>=2.3.1",
    "opencv-python>=*********",
    "pillow>=11.3.0",
    "protobuf>=6.31.1",
    "psutil>=7.0.0",
    "psycopg2-binary>=2.9.10",
    "pytest-asyncio>=1.1.0",
    "python-dotenv>=1.1.1",
    "pyyaml>=6.0.2",
    "redis>=6.2.0",
    "sqlalchemy>=2.0.41",
    "watchdog>=6.0.0",
]

[tool.ruff]
line-length = 120
exclude = [
  "node_modules",
  "website",
  "dashboard/frontend"
]

[tool.ruff.lint]
select = ["E", "F"]
ignore = ["E501"]

[dependency-groups]
dashboard-backend = [
    "aiohttp>=3.12.14",
    "asyncpg>=0.30.0",
    "cachetools>=6.1.0",
    "fastapi>=0.116.1",
    "psycopg2-binary>=2.9.10",
    "pyjwt>=2.10.1",
    "python-dotenv>=1.1.1",
    "python-jose>=3.5.0",
    "redis>=6.2.0",
    "requests>=2.32.4",
    "sqlalchemy>=2.0.41",
    "uvicorn>=0.35.0",
]
dev = [
    "pyright>=1.1.403",
    "pytest>=8.4.1",
    "ruff>=0.12.4",
]
