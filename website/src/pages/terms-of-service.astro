---
import Layout from '@/layouts/Layout.astro';
---

<Layout
    title="Terms of Service"
    description="Review the OpenGuard terms that govern the use of our bot and dashboard."
>
    <main class="container py-12">
        <div class="prose dark:prose-invert max-w-none">
            <h1>Terms of Service</h1>
            <p>
                By using OpenGuard and its dashboard, you agree to the following terms:
            </p>
            <ul>
                <li>
                    <strong>Eligibility:</strong> You must comply with Discord's Terms of Service and Community Guidelines.
                </li>
                <li>
                    <strong>Bot Usage:</strong> OpenGuard is provided "as is" for moderation and analytics purposes. We do not guarantee uninterrupted service or error-free operation.
                </li>
                <li>
                    <strong>Data Storage:</strong> Moderation logs, infractions, appeals, and server configuration data are stored securely for the purpose of providing bot and dashboard functionality.
                </li>
                <li>
                    <strong>Prohibited Use:</strong> You may not use OpenGuard for illegal activities, harassment, or to circumvent <PERSON>rd's rules.
                </li>
                <li>
                    <strong>Limitation of Liability:</strong> We are not liable for any damages or losses resulting from the use or inability to use OpenGuard.
                </li>
                <li>
                    <strong>Changes:</strong> We may update these terms at any time. Continued use of OpenGuard constitutes acceptance of any changes.
                </li>
            </ul>
            <p>
                For questions or concerns, please contact the project maintainers.
            </p>
        </div>
    </main>
</Layout>