---
import Layout from '@/layouts/Layout.astro';
---

<Layout
    title="Privacy Policy"
    description="Read how OpenGuard collects, stores, and uses data to operate our Discord bot and dashboard."
>
    <main class="container py-12">
        <div class="prose dark:prose-invert max-w-none">
            <h1>Privacy Policy</h1>
            <p>
                OpenGuard values your privacy. We only collect data strictly required for bot and dashboard functionality, and usage analytics and error logs to improve our services.
                This policy explains what data we collect, how it is used, and your rights.
            </p>
            <h2>What We Collect</h2>
            <ul>
                <li>
                    <strong>Discord Data:</strong> User IDs, guild IDs, moderation actions, infractions, appeals, and server configuration. Only data required for bot operation and moderation features is collected.
                </li>
                <li>
                    <strong>Custom User Data:</strong> Data provided for bot features (such as permissions or analytics) is stored securely and, where applicable, encrypted. No extra personal data is collected beyond what is needed for functionality.
                </li>
                <li>
                        <strong>Bot/Dashboard Usage Metrics & Analytics, Error Reports:</strong> Usage statistics, analytics, and error reports from the bot and dashboard may be analyzed by maintainers to improve the project. These are not sold or given to third parties. No advertising, third-party tracking, or profiling is performed.
                </li>
                <li>
                    <strong>Cookies/Local Storage:</strong> Used only for session management and dashboard functionality. No tracking or advertising cookies are used.
                </li>
            </ul>
            <h2>How We Use Data</h2>
            <ul>
                <li>To provide essential moderation, logging, and analytics features.</li>
                <li>To improve bot and dashboard performance and reliability.</li>
                <li>To help server admins manage their communities.</li>
            </ul>
            <h2>Data Security</h2>
            <ul>
                <li>Data is stored securely and, where possible, encrypted.</li>
                <li>Access is restricted to authorized maintainers. No data is sold or shared with third parties.</li>
            </ul>
            <h2>Your Rights</h2>
            <ul>
                <li>You may request deletion of your data by contacting the maintainers.</li>
                <li>We do not sell or share your data with third parties.</li>
            </ul>
            <h2>Changes</h2>
            <p>
                This policy may be updated as the project evolves. Continued use of OpenGuard constitutes acceptance of any changes.
            </p>
            <p>
                For questions or data requests, contact the project maintainers.
            </p>
        </div>
    </main>
</Layout>